"""
Unit tests for the Spark Job Cleanup Watchdog DAG.

This module provides comprehensive test coverage for the spark_cleanup_watchdog
module, including tests for pod processing, database queries, Kubernetes operations,
and safety measures.
"""

from __future__ import annotations

import pytest
from datetime import datetime, timedelta
from unittest.mock import Mock, patch
from kubernetes.client import V1Pod, V1ObjectMeta
from kubernetes.client.rest import ApiException

# Import the module under test
import sys
import os

sys.path.insert(0, os.path.join(os.path.dirname(__file__), "../../../../../dags"))


from spark_cleanup_watchdog import (
    cleanup_orphaned_spark_jobs,
    _process_spark_pod,
    _get_task_instance_state,
    _extract_spark_application_name,
    _delete_spark_application,
)

_TEST_ADIP_INSTANCE_ID = "no-adip-id-likely-to-fail"

@pytest.fixture(scope="module", autouse=True)
def mock_airflow_variables():
    """Mock Airflow variables for testing."""
    with patch("airflow.models.Variable.get") as mock_get:
        mock_get.side_effect = lambda key, default=None: {
            "adip_instance_id": _TEST_ADIP_INSTANCE_ID
        }.get(key, default)
        yield mock_get

class TestSparkCleanupWatchdog:
    """Test cases for the main cleanup function."""

    @patch("spark_cleanup_watchdog.KubernetesHook")
    @patch("spark_cleanup_watchdog._process_spark_pod")
    def test_cleanup_orphaned_spark_jobs_success(
        self, mock_process_pod, mock_k8s_hook_class
    ):
        """
        Test successful execution of the main cleanup function.

        Verifies that the function correctly processes multiple pods and
        aggregates results properly.
        """
        # Given: Mock Kubernetes hook and pod list
        mock_k8s_hook = Mock()
        mock_k8s_hook_class.return_value = mock_k8s_hook

        # Create mock pods
        pod1 = self._create_mock_pod("spark-app-1-driver", "spark-operator")
        pod2 = self._create_mock_pod("spark-app-2-driver", "spark-operator")

        mock_pod_list = Mock()
        mock_pod_list.items = [pod1, pod2]
        mock_core_api = Mock()
        mock_core_api.list_namespaced_pod.return_value = mock_pod_list
        mock_k8s_hook.core_v1_client = mock_core_api

        # Mock process results
        mock_process_pod.side_effect = ["cleaned", "skipped"]

        # Mock context
        context = {"dag_run": Mock(conf={"min_pod_age_seconds": 900, "dry_run": False})}

        # When: Calling the cleanup function
        cleanup_orphaned_spark_jobs(**context)

        # Then: Should list pods and process each one
        mock_core_api.list_namespaced_pod.assert_called_once_with(
            namespace="spark-operator", label_selector="spark-role=driver"
        )

        assert mock_process_pod.call_count == 2
        mock_process_pod.assert_any_call(pod1, 900, False, mock_k8s_hook, _TEST_ADIP_INSTANCE_ID)
        mock_process_pod.assert_any_call(pod2, 900, False, mock_k8s_hook, _TEST_ADIP_INSTANCE_ID)

    def _create_mock_pod(
        self,
        name: str,
        namespace: str,
        annotations: dict | None = None,
        creation_timestamp: datetime | None = None,
    ):
        """Helper method to create mock pod objects."""
        if annotations is None:
            annotations = self._create_valid_annotations()

        if creation_timestamp is None:
            creation_timestamp = datetime.now() - timedelta(
                seconds=1800
            )  # 30 minutes ago

        pod = Mock(spec=V1Pod)
        pod.metadata = Mock(spec=V1ObjectMeta)
        pod.metadata.name = name
        pod.metadata.namespace = namespace
        pod.metadata.annotations = annotations
        pod.metadata.creation_timestamp = creation_timestamp
        pod.metadata.labels = {
            "sparkoperator.k8s.io/app-name": f"{name[:-7]}"
        }  # Remove '-driver'

        return pod

    def _create_valid_annotations(self):
        """Helper method to create valid pod annotations."""
        return {
            "aetion.com/airflow-dag-id": "test_dag",
            "aetion.com/airflow-task-id": "test_task",
            "aetion.com/airflow-run-id": "test_run",
            "aetion.com/adip-instance-id": "no-adip-id-likely-to-fail",  # Matches default
            "aetion.com/airflow-try-number": "1",
        }

    @patch("spark_cleanup_watchdog.KubernetesHook")
    def test_cleanup_orphaned_spark_jobs_kubernetes_error(self, mock_k8s_hook_class):
        """
        Test handling of Kubernetes API errors.

        Verifies that Kubernetes exceptions are properly caught and re-raised.
        """
        # Given: Mock Kubernetes hook that raises an exception
        mock_k8s_hook = Mock()
        mock_k8s_hook_class.return_value = mock_k8s_hook
        mock_core_api = Mock()
        mock_core_api.list_namespaced_pod.side_effect = ApiException("API Error")
        mock_k8s_hook.core_v1_client = mock_core_api

        context = {"dag_run": Mock(conf={"min_pod_age_seconds": 900, "dry_run": False})}

        # When/Then: Should raise the Kubernetes exception
        with pytest.raises(ApiException):
            cleanup_orphaned_spark_jobs(**context)

    def test_cleanup_orphaned_spark_jobs_default_parameters(self):
        """
        Test that default parameters are used when not provided in DAG run config.

        Verifies that the function handles missing configuration gracefully.
        """
        # Given: Context with empty DAG run config
        context = {"dag_run": Mock(conf={})}

        with patch("spark_cleanup_watchdog.KubernetesHook") as mock_k8s_hook_class:
            mock_k8s_hook = Mock()
            mock_k8s_hook_class.return_value = mock_k8s_hook
            mock_pod_list = Mock()
            mock_pod_list.items = []
            mock_core_api = Mock()
            mock_core_api.list_namespaced_pod.return_value = mock_pod_list
            mock_k8s_hook.core_v1_client = mock_core_api

            # When: Calling the cleanup function
            cleanup_orphaned_spark_jobs(**context)

            # Then: Should use default values (verified through successful execution)
            mock_core_api.list_namespaced_pod.assert_called_once()


class TestProcessSparkPod:
    """Test cases for individual pod processing logic."""

    def test_process_spark_pod_missing_annotations(self):
        """
        Test handling of pods with missing required annotations.

        Verifies that pods without proper annotations are skipped with warnings.
        """
        # Given: Pod with missing annotations
        pod = self._create_mock_pod("test-pod", "spark-operator", annotations={})
        mock_k8s_hook = Mock()

        # When: Processing the pod
        result = _process_spark_pod(pod, 900, False, mock_k8s_hook, adip_instance_id=_TEST_ADIP_INSTANCE_ID)

        # Then: Should skip the pod
        assert result == "skipped"

    def test_process_spark_pod_wrong_adip_instance(self):
        """
        Test handling of pods from different ADIP instances.

        Verifies that pods from other instances are skipped for safety.
        """
        # Given: Pod with different ADIP instance ID
        annotations = {
            "aetion.com/airflow-dag-id": "test_dag",
            "aetion.com/airflow-task-id": "test_task",
            "aetion.com/airflow-run-id": "test_run",
            "aetion.com/adip-instance-id": "different-instance",
            "aetion.com/airflow-try-number": "1",
        }
        pod = self._create_mock_pod(
            "test-pod", "spark-operator", annotations=annotations
        )
        mock_k8s_hook = Mock()

        # When: Processing the pod
        result = _process_spark_pod(pod, 900, False, mock_k8s_hook, adip_instance_id=_TEST_ADIP_INSTANCE_ID)

        # Then: Should skip the pod
        assert result == "skipped"

    def test_process_spark_pod_too_young(self):
        """
        Test handling of pods that are too young for cleanup.

        Verifies that recently created pods are not cleaned up.
        """
        # Given: Recently created pod
        annotations = self._create_valid_annotations()
        creation_time = datetime.now() - timedelta(seconds=300)  # 5 minutes ago
        pod = self._create_mock_pod(
            "test-pod",
            "spark-operator",
            annotations=annotations,
            creation_timestamp=creation_time,
        )
        mock_k8s_hook = Mock()

        # When: Processing the pod with 15-minute threshold
        result = _process_spark_pod(pod, 900, False, mock_k8s_hook, adip_instance_id=_TEST_ADIP_INSTANCE_ID)

        # Then: Should skip the pod
        assert result == "skipped"

    @patch("spark_cleanup_watchdog._get_task_instance_state")
    def test_process_spark_pod_task_not_failed(self, mock_get_state):
        """
        Test handling of pods with non-failed task states.

        Verifies that only failed tasks are considered for cleanup.
        """
        # Given: Old pod with running task
        annotations = self._create_valid_annotations()
        creation_time = datetime.now() - timedelta(seconds=1800)  # 30 minutes ago
        pod = self._create_mock_pod(
            "test-pod",
            "spark-operator",
            annotations=annotations,
            creation_timestamp=creation_time,
        )
        mock_k8s_hook = Mock()
        mock_get_state.return_value = "running"

        # When: Processing the pod
        result = _process_spark_pod(pod, 900, False, mock_k8s_hook, adip_instance_id=_TEST_ADIP_INSTANCE_ID)

        # Then: Should skip the pod
        assert result == "skipped"

    @patch("spark_cleanup_watchdog._delete_spark_application")
    @patch("spark_cleanup_watchdog._extract_spark_application_name")
    @patch("spark_cleanup_watchdog._get_task_instance_state")
    def test_process_spark_pod_cleanup_success(
        self, mock_get_state, mock_extract_name, mock_delete
    ):
        """
        Test successful cleanup of a failed pod.

        Verifies the complete cleanup workflow for a pod that meets all criteria.
        """
        # Given: Old pod with failed task
        annotations = self._create_valid_annotations()
        creation_time = datetime.now() - timedelta(seconds=1800)  # 30 minutes ago
        pod = self._create_mock_pod(
            "test-pod",
            "spark-operator",
            annotations=annotations,
            creation_timestamp=creation_time,
        )
        mock_k8s_hook = Mock()
        mock_get_state.return_value = "failed"
        mock_extract_name.return_value = "spark-app-123"
        mock_delete.return_value = "cleaned"

        # When: Processing the pod
        result = _process_spark_pod(pod, 900, False, mock_k8s_hook, adip_instance_id=_TEST_ADIP_INSTANCE_ID)

        # Then: Should clean the pod
        assert result == "cleaned"
        mock_delete.assert_called_once_with(
            "spark-app-123", "spark-operator", mock_k8s_hook
        )

    @patch("spark_cleanup_watchdog._extract_spark_application_name")
    @patch("spark_cleanup_watchdog._get_task_instance_state")
    def test_process_spark_pod_dry_run(self, mock_get_state, mock_extract_name):
        """
        Test dry run mode functionality.

        Verifies that dry run mode logs actions without performing deletions.
        """
        # Given: Old pod with failed task in dry run mode
        annotations = self._create_valid_annotations()
        creation_time = datetime.now() - timedelta(seconds=1800)  # 30 minutes ago
        pod = self._create_mock_pod(
            "test-pod",
            "spark-operator",
            annotations=annotations,
            creation_timestamp=creation_time,
        )
        mock_k8s_hook = Mock()
        mock_get_state.return_value = "failed"
        mock_extract_name.return_value = "spark-app-123"

        # When: Processing the pod in dry run mode
        result = _process_spark_pod(pod, 900, True, mock_k8s_hook, adip_instance_id=_TEST_ADIP_INSTANCE_ID)

        # Then: Should return cleaned but not call delete
        assert result == "cleaned"

    def _create_mock_pod(
        self,
        name: str,
        namespace: str,
        annotations: dict | None = None,
        creation_timestamp: datetime | None = None,
    ):
        """Helper method to create mock pod objects."""
        if annotations is None:
            annotations = self._create_valid_annotations()

        if creation_timestamp is None:
            creation_timestamp = datetime.now() - timedelta(
                seconds=1800
            )  # 30 minutes ago

        pod = Mock(spec=V1Pod)
        pod.metadata = Mock(spec=V1ObjectMeta)
        pod.metadata.name = name
        pod.metadata.namespace = namespace
        pod.metadata.annotations = annotations
        pod.metadata.creation_timestamp = creation_timestamp
        pod.metadata.labels = {
            "sparkoperator.k8s.io/app-name": f"{name[:-7]}"
        }  # Remove '-driver'

        return pod

    def _create_valid_annotations(self):
        """Helper method to create valid pod annotations."""
        return {
            "aetion.com/airflow-dag-id": "test_dag",
            "aetion.com/airflow-task-id": "test_task",
            "aetion.com/airflow-run-id": "test_run",
            "aetion.com/adip-instance-id": "no-adip-id-likely-to-fail",  # Matches default
            "aetion.com/airflow-try-number": "1",
        }


class TestGetTaskInstanceState:
    """Test cases for database query functionality."""

    @patch("spark_cleanup_watchdog.create_session")
    def test_get_task_instance_state_found(self, mock_create_session):
        """
        Test successful retrieval of task instance state.

        Verifies that the function correctly queries the database and returns task state.
        """
        # Given: Mock database session with task instance
        mock_session = Mock()
        mock_create_session.return_value.__enter__.return_value = mock_session

        mock_task_instance = Mock()
        mock_task_instance.state = "failed"
        mock_session.query.return_value.filter.return_value.first.return_value = (
            mock_task_instance
        )

        # When: Querying for task state
        result = _get_task_instance_state("test_dag", "test_task", "test_run", 1)

        # Then: Should return the task state
        assert result == "failed"

    @patch("spark_cleanup_watchdog.create_session")
    def test_get_task_instance_state_not_found(self, mock_create_session):
        """
        Test handling of missing task instances.

        Verifies that the function returns None when task instance is not found.
        """
        # Given: Mock database session with no task instance
        mock_session = Mock()
        mock_create_session.return_value.__enter__.return_value = mock_session
        mock_session.query.return_value.filter.return_value.first.return_value = None

        # When: Querying for non-existent task
        result = _get_task_instance_state("test_dag", "test_task", "test_run", 1)

        # Then: Should return None
        assert result is None

    @patch("spark_cleanup_watchdog.create_session")
    def test_get_task_instance_state_database_error(self, mock_create_session):
        """
        Test handling of database errors.

        Verifies that database exceptions are caught and None is returned.
        """
        # Given: Mock database session that raises an exception
        mock_create_session.side_effect = Exception("Database error")

        # When: Querying with database error
        result = _get_task_instance_state("test_dag", "test_task", "test_run", 1)

        # Then: Should return None
        assert result is None


class TestExtractSparkApplicationName:
    """Test cases for SparkApplication name extraction."""

    def test_extract_spark_application_name_success(self):
        """
        Test successful extraction of SparkApplication name.

        Verifies that the function correctly extracts the name from pod labels.
        """
        # Given: Pod with SparkApplication label
        pod = Mock()
        pod.metadata.name = "test-pod"
        pod.metadata.labels = {"sparkoperator.k8s.io/app-name": "spark-app-123"}

        # When: Extracting the name
        result = _extract_spark_application_name(pod)

        # Then: Should return the application name
        assert result == "spark-app-123"

    def test_extract_spark_application_name_missing_label(self):
        """
        Test handling of missing SparkApplication label.

        Verifies that the function returns None when the required label is missing.
        """
        # Given: Pod without SparkApplication label
        pod = Mock()
        pod.metadata.name = "test-pod"
        pod.metadata.labels = {}

        # When: Extracting the name
        result = _extract_spark_application_name(pod)

        # Then: Should return None
        assert result is None

    def test_extract_spark_application_name_no_labels(self):
        """
        Test handling of pods with no labels.

        Verifies that the function handles None labels gracefully.
        """
        # Given: Pod with None labels
        pod = Mock()
        pod.metadata.name = "test-pod"
        pod.metadata.labels = None

        # When: Extracting the name
        result = _extract_spark_application_name(pod)

        # Then: Should return None
        assert result is None


class TestDeleteSparkApplication:
    """Test cases for SparkApplication deletion."""

    def test_delete_spark_application_success(self):
        """
        Test successful deletion of SparkApplication.

        Verifies that the function correctly calls the Kubernetes API for deletion.
        """
        # Given: Mock Kubernetes hook
        mock_k8s_hook = Mock()

        # When: Deleting SparkApplication
        result = _delete_spark_application(
            "spark-app-123", "spark-operator", mock_k8s_hook
        )

        # Then: Should call delete API and return success
        mock_k8s_hook.delete_custom_object.assert_called_once_with(
            group="sparkoperator.k8s.io",
            version="v1beta2",
            namespace="spark-operator",
            plural="sparkapplications",
            name="spark-app-123",
        )
        assert result == "cleaned"

    def test_delete_spark_application_not_found(self):
        """
        Test handling of already-deleted SparkApplications.

        Verifies that 404 errors are handled gracefully.
        """
        # Given: Mock Kubernetes hook that returns 404
        mock_k8s_hook = Mock()

        api_exception = ApiException(status=404)
        mock_k8s_hook.delete_custom_object.side_effect = api_exception

        # When: Deleting non-existent SparkApplication
        result = _delete_spark_application(
            "spark-app-123", "spark-operator", mock_k8s_hook
        )

        # Then: Should handle gracefully and return cleaned
        assert result == "cleaned"

    def test_delete_spark_application_api_error(self):
        """
        Test handling of Kubernetes API errors.

        Verifies that non-404 API errors are handled appropriately.
        """
        # Given: Mock Kubernetes hook that returns API error
        mock_k8s_hook = Mock()

        api_exception = ApiException(status=500)
        mock_k8s_hook.delete_custom_object.side_effect = api_exception

        # When: Deleting with API error
        result = _delete_spark_application(
            "spark-app-123", "spark-operator", mock_k8s_hook
        )

        # Then: Should return error
        assert result == "error"
