"""
Integration-ish tests for the clean_orphan_k8s_spark_jobs DAG.

These tests execute the TaskFlow tasks end-to-end while intercepting
external interactions (Kubernetes HTTP and Slack posting). We avoid
mocking internal business logic and only stub external boundaries.

Python: 3.10
Pytest best practices: type hints, fixtures, docstrings.
"""

from __future__ import annotations

from dataclasses import dataclass
from types import SimpleNamespace
from typing import Any, Dict, Iterable, List

from airflow import DAG
import pytest

from airflow.models import TaskInstance, Variable, DagRun, DagModel
from airflow.models.pool import Pool
from airflow.utils import timezone
from airflow.utils.state import State, TaskInstanceState, DagRunState
from airflow.settings import Session

# DAG under test
from aetion.adip.airflow.pipelines.orphan_k8s_spark_cleaner.dag_builder import (
    clean_orphan_k8s_spark_jobs,
)

# Optional type info for status (won’t import-fail if module changes)
try:
    from aetion.adip.airflow.pipelines.orphan_k8s_spark_cleaner.utils import (
        CleanupStatus,
    )  # noqa: F401
except Exception:  # pragma: no cover
    pass


# -----------------------------
# Fixtures: fake external systems (<PERSON>bernetes, Slack)
# -----------------------------


@dataclass
class _FakePod:
    """Minimal pod shape used by utils: only the metadata subtree is needed."""

    name: str
    namespace: str
    annotations: Dict[str, str]
    labels: Dict[str, str]
    creation_ts: Any

    def to_obj(self) -> Any:
        return SimpleNamespace(
            metadata=SimpleNamespace(
                name=self.name,
                namespace=self.namespace,
                annotations=self.annotations,
                labels=self.labels,
                creation_timestamp=self.creation_ts,
            )
        )


@pytest.fixture()
def slack_capture(monkeypatch: pytest.MonkeyPatch) -> List[Dict[str, Any]]:
    """Capture calls to SlackAPIPostOperator.execute without hitting Slack.

    We stub the provider operator's execute and record the templated payload.
    """
    calls: List[Dict[str, Any]] = []

    def _fake_execute(self, context: Dict[str, Any]) -> None:  # type: ignore[override]
        calls.append(
            {
                "task_id": self.task_id,
                "channel": getattr(self, "channel", None),
                "text": getattr(self, "text", None),
                "blocks": getattr(self, "blocks", None),
            }
        )
        return None

    monkeypatch.setattr(
        "airflow.providers.slack.operators.slack.SlackAPIPostOperator.execute",
        _fake_execute,
        raising=True,
    )
    return calls


@pytest.fixture()
def fake_kubernetes_env(monkeypatch: pytest.MonkeyPatch) -> Dict[str, Any]:
    """Provide a fake KubernetesHook and capture delete calls.

    The fake exposes only the attributes/methods used by the DAG utils:
      - core_v1_client.list_namespaced_pod(namespace, label_selector=...)
      - delete_custom_object(...)
    """
    state: Dict[str, Any] = {"pods": [], "deletes": []}

    class _CoreV1:
        def list_namespaced_pod(self, namespace: str, label_selector: str = "") -> Any:  # noqa: D401
            return SimpleNamespace(items=[p.to_obj() for p in state["pods"]])

    class _FakeKubernetesHook:
        def __init__(self, *args: Any, **kwargs: Any) -> None:
            self.core_v1_client = _CoreV1()

        def delete_custom_object(
            self,
            *,
            group: str,
            version: str,
            namespace: str,
            plural: str,
            name: str,
        ) -> None:
            state["deletes"].append((group, version, namespace, plural, name))

    monkeypatch.setattr(
        "aetion.adip.airflow.pipelines.orphan_k8s_spark_cleaner.dag_builder.KubernetesHook",
        _FakeKubernetesHook,
        raising=True,
    )

    return state


# -----------------------------
# Helper: seed a FAILED TaskInstance row to drive cleanup logic
# -----------------------------


def _seed_ti_failed(
    *, dag: DAG, dag_id: str, task_id: str, run_id: str, try_number: int = 1
) -> None:
    """Insert a FAILED TaskInstance row matching the pod annotations.

    We write directly to the metadata DB so the cleanup logic can find a
    matching failed task without mocking internal code.
    """
    with Session() as session:
        # Create a fake DagRun for the TI to point to, avoiding FK violations
        dag_run = DagRun(
            dag_id=dag_id,
            run_id=run_id,
            run_type="manual",
            execution_date=timezone.utcnow(),
            state=DagRunState.FAILED,
        )
        session.add(dag_run)
        session.commit()

        ti = TaskInstance(dag.get_task(task_id), run_id=run_id, map_index=-1)
        ti.state = State.FAILED
        ti.try_number = try_number
        session.add(ti)
        session.commit()


# -----------------------------
# Tests
# -----------------------------


def test_dag_structure() -> None:
    """DAG factory produces expected tasks and dependency graph."""
    dag = clean_orphan_k8s_spark_jobs()
    task_ids = {t.task_id for t in dag.tasks}
    assert {"clean_orphan_k8s_spark_jobs", "notify_to_slack"} <= task_ids
    downstream = {
        t.task_id for t in dag.get_task("clean_orphan_k8s_spark_jobs").downstream_list
    }
    assert downstream == {"notify_to_slack"}


@pytest.mark.usefixtures("fake_kubernetes_env")
def test_end_to_end_no_pods(slack_capture: List[Dict[str, Any]]) -> None:
    """When there are no driver pods, notifier should not post to Slack."""
    dag = clean_orphan_k8s_spark_jobs()
    exec_date = timezone.utcnow()

    dag.create_dagrun(
        run_id="test_run",
        state=State.RUNNING,
        execution_date=exec_date,
    )

    # Run producer task
    ti_clean = TaskInstance(dag.get_task("clean_orphan_k8s_spark_jobs"), exec_date)
    ti_clean.run(ignore_ti_state=True)

    # Run notifier
    ti_notify = TaskInstance(dag.get_task("notify_to_slack"), exec_date)
    ti_notify.run(ignore_ti_state=True)

    assert slack_capture == []


@pytest.mark.usefixtures("fake_kubernetes_env")
def test_end_to_end_cleaned_pods(
    slack_capture: List[Dict[str, Any]],
    fake_kubernetes_env: Dict[str, Any],
) -> None:
    """When pods correspond to FAILED TIs, cleanup should trigger and Slack should post."""
    dag = clean_orphan_k8s_spark_jobs()
    exec_date = timezone.utcnow()

    with Session() as session:
        session.add(DagModel(dag_id="test_dag", is_active=True, is_paused=False))
        session.commit()

    # Arrange two pods with matching FAILED TIs
    for i in range(2):
        dag_id = "test_dag"
        task_id = "test_task"
        run_id = f"manual__{i}"
        _seed_ti_failed(
            dag=dag, dag_id=dag_id, task_id=task_id, run_id=run_id, try_number=1
        )
        pod = _FakePod(
            name=f"spark-driver-{i}",
            namespace="spark-operator",
            annotations={
                "aetion.com/airflow-dag-id": dag_id,
                "aetion.com/airflow-task-id": task_id,
                "aetion.com/airflow-run-id": run_id,
                "aetion.com/airflow-try-number": "1",
                "aetion.com/adip-instance-id": "adip-prod",
            },
            labels={"sparkoperator.k8s.io/app-name": f"spark-app-{i}"},
            creation_ts=timezone.utcnow() - timezone.timedelta(minutes=30),
        )
        fake_kubernetes_env["pods"].append(pod)

    dag.create_dagrun(
        run_id="test_run",
        state=State.RUNNING,
        execution_date=exec_date,
    )

    # Run producer
    ti_clean = TaskInstance(dag.get_task("clean_orphan_k8s_spark_jobs"), exec_date)
    ti_clean.run(ignore_ti_state=True)

    # Run notifier
    ti_notify = TaskInstance(dag.get_task("notify_to_slack"), exec_date)
    ti_notify.run(ignore_ti_state=True)

    # We expect exactly one Slack message summarizing the cleanup
    assert len(slack_capture) == 1
    msg = slack_capture[0]
    # Channel is templated from Variable
    assert msg["channel"] == "#adip-notifications"
    # Message or blocks should mention at least one cleaned pod/app name
    rendered = (msg.get("text") or "") + str(msg.get("blocks") or "")
    assert "spark-app-0" in rendered or "spark-driver-0" in rendered

    # Also ensure our fake delete was called for both apps
    deletes = fake_kubernetes_env["deletes"]
    assert {d[-1] for d in deletes} == {"spark-app-0", "spark-app-1"}
