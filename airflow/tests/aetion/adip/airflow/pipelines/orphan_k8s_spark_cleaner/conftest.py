"""
Shared fixtures for orphan_k8s_spark_cleaner DAG tests.
"""
from __future__ import annotations

from typing import Iterable

import pytest
from airflow.models import Variable
from airflow.utils import db


@pytest.fixture(autouse=True)
def airflow_test_environment(monkeypatch: pytest.MonkeyPatch) -> Iterable[None]:
    """
    Sets up a temporary Airflow environment for testing.

    - Configures an in-memory SQLite database & initializes it.
    - Runs Airflow in unit test mode with examples disabled.
    - Provides required Variables for the DAG under test.
    """
    monkeypatch.setenv("AIRFLOW__CORE__SQL_ALCHEMY_CONN", "sqlite:///:memory:")
    monkeypatch.setenv("AIRFLOW__CORE__UNIT_TEST_MODE", "True")
    monkeypatch.setenv("AIRFLOW__CORE__LOAD_EXAMPLES", "False")

    db.initdb()

    Variable.set("adip_instance_id", "adip-prod")
    Variable.set("slack_channel", "#adip-notifications")
    Variable.set("airflow_base_url", "http://airflow.local:8080")

    try:
        yield
    finally:
        db.resetdb()
