import logging
import os
import re
from pathlib import Path
from urllib.parse import urlparse, urlunparse, urlencode

from airflow import configuration
from airflow.exceptions import AirflowNotFoundException
from airflow.operators.python import get_current_context
from airflow.providers.slack.hooks.slack import <PERSON>lackHook
from airflow.providers.slack.operators.slack import SlackAPIPostOperator

logger = logging.getLogger(__name__)


def reformat_url(raw_url, base_url):
    if not base_url:
        return raw_url

    raw_url = urlparse(raw_url)
    base_url = urlparse(base_url)

    print(raw_url.scheme, base_url.netloc, raw_url.path, raw_url.params, raw_url.query, raw_url.fragment)

    return urlunparse(
        [raw_url.scheme, base_url.netloc, raw_url.path, raw_url.params, raw_url.query, raw_url.fragment])


def get_log_filepath(context):
    ti = context.get('task_instance')
    iso = ti.execution_date.isoformat()
    log = os.path.expanduser(configuration.conf.get('logging', 'BASE_LOG_FOLDER'))
    try_number = ti.try_number - 1
    return f"{log}/{ti.dag_id}/{ti.task_id}/{iso}/{try_number}.log"


def extract_databricks_link(context):
    log_filepath = get_log_filepath(context)
    if not os.path.exists(log_filepath):
        return None

    with open(log_filepath, "r+") as f_in:
        for line in f_in:
            matches = re.search('(https://)(.*?).cloud.databricks.com(.*?)/(.*)', line)
            if matches:
                return matches.group(0)

    return None


def fire_success_slack_notification(title, slack_channel, base_url, context, alert_user):
    ti = context.get('task_instance')
    execution_date = context.get('execution_date')
    airflow_log_url = reformat_url(context.get('task_instance').log_url, base_url)

    message = f"""
        :large_green_circle: {title}
            *Task*: {ti.task_id}
            *Dag*: {ti.dag_id}
            *Execution Time*: {execution_date}
            *Airflow Log Url*: {airflow_log_url}
            *Alert User*: <{alert_user}>
        """

    return SlackAPIPostOperator(
        task_id=ti.task_id,
        channel=slack_channel,
        text=message
    ).execute(context=context)


def fire_task_fail_slack_alert(slack_channel, base_url, context, alert_user):
    ti = context.get('task_instance')
    execution_date = context.get('execution_date')
    databricks_log_url = extract_databricks_link(context)
    airflow_log_url = reformat_url(context.get('task_instance').log_url, base_url)

    return SlackAPIPostOperator(
        task_id=context.get('task_instance').task_id,
        channel=slack_channel,
        text=f"""
            :red_circle: Task Failed.
            *Task*: {ti.task_id}
            *Dag*: {ti.dag_id}
            *Execution Time*: {execution_date}
            *Databricks Log Url*: {databricks_log_url}
            *Airflow Log Url*: {airflow_log_url}
            *Alert User*: <{alert_user}>
            """
    ).execute(context=context)


def fire_task_stopgap_slack_alert(slack_channel, base_url, context, alert_user):
    ti = context.get('task_instance')
    execution_date = context.get('execution_date')

    return SlackAPIPostOperator(
        task_id=context.get('task_instance').task_id,
        channel=slack_channel,
        text=f"""
            :black_square_for_stop: Stopgap.
            *Task*: {ti.task_id}
            *Dag*: {ti.dag_id}
            *Execution Time*: {execution_date}
            *Alert User*: <{alert_user}>
            """
    ).execute(context=context)


def send_gdr_and_spec_slack_message(
    context,
    dataset: str,
    client: str,
    revision: str,
    slack_channel: str,
    amazon_console_s3_link: str,
    partition_full_parquet_url: str,
    alert_user: str,
    alert_scientist: str,
    base_url: str,
    preprocess: bool,
    catalog_overrides: dict,
) -> None:
    """
    Send a stopgap Slack alert with GDR/spec update details and a Scala command to load the data.

    Args:
        context: Airflow context containing execution date and task instance.
        dataset (str): DBC name of the data.
        client (str): Client identifier from s3 bucket.
        revision (str): Revision string.
        slack_channel (str): Slack channel to post messages to.
        amazon_console_s3_link (str): Amazon console S3 URL for the partition full parquet data.
        alert_user (str): User or group to mention in the alert.
        alert_scientist (str): Scientist to mention in the alert.
        catalog_overrides (dict): example:
            {
                "tableOverrides": {
                    "enrollment": {
                        "baseUrl": "s3a://lilly.aetion.com/etl/maternity/20241031/partition-full-parquet/",
                        "path": "mom_enrollment"
                    },
                    "medical_claims": {
                        "baseUrl": "s3a://lilly.aetion.com/etl/maternity/20241031/partition-full-parquet/",
                        "path": "mom_medical_claims"
                    }
                }
            }
    """

    logger.info(f"preprocess:\t{preprocess}")
    logger.info(f"client:\t{client}")
    logger.info(f"dataset:\t{dataset}")
    logger.info(f"revision:\t{revision}")
    base_dir = Path("dags/resources/slack_messages/check_gdr_and_spec")
    detail_template_path = base_dir / 'detail_template.txt'

    scala_template_path = base_dir / 'scala_template.txt'
    # dataUrl
    dynamic_catalog = 'val dataUrl = s"s3a://'
    s3_prefix = '$client.aetion.com/etl/$dataset/$revision/'
    if preprocess:
        dynamic_catalog += f'{s3_prefix}preprocessed-full-parquet'
    else:
        if partition_full_parquet_url:
            if partition_full_parquet_url.endswith("/"):
                partition_full_parquet_url = partition_full_parquet_url[:-1]
            dynamic_catalog += partition_full_parquet_url
        else:
            dynamic_catalog += f'{s3_prefix}partition-full-parquet'
    dynamic_catalog += '/"\n'
    # catalogOverrides
    if catalog_overrides:
        dynamic_catalog += 'val catalogOverrides = CatalogOverrides(\n  Map(\n'
        tables = []
        for table_name, val in catalog_overrides.get("tableOverrides",{}).items():
            base_url = val.get("baseUrl", None)
            path = val.get("path", None)
            tables.append(f'    "{table_name}" -> DataTableOverrides(baseUrl = Some("{base_url}"), path=Some("{path}"))')
        dynamic_catalog += ",\n".join(tables)
        dynamic_catalog += '\n  )\n)\nval catalogOverridesJson = YamlCatalog.toOverridesJson(catalogOverrides)\n'
    # catalog
    dynamic_catalog += 'val catalog = YamlCatalog(spark, dataUrl, dataFormat, '
    args = [
        'Some(catalogOverrides)' if catalog_overrides else 'None',
        'Some(true)' if preprocess else 'Some(false)',
    ]
    dynamic_catalog += ', '.join(args) + ')'


    execution_date, airflow_url = get_execution_date_and_airflow_url_from_context(context, base_url)
    # Load and format detail and scala message template
    with open(detail_template_path, 'r') as f:
        detail_text = f.read().format(
            dataset=dataset,
            client=client,
            revision=revision,
            s3_url=amazon_console_s3_link,
            airflow_url=airflow_url,
            alert_user=alert_user,
            alert_scientist=alert_scientist,
            execution_date=execution_date,
            base_url=base_url
        )

    # Load and format scala command template
    with open(scala_template_path, 'r') as f:
        scala_text = f.read().format(
            dataset=dataset,
            client=client,
            revision=revision,
            dynamic_catalog=dynamic_catalog,
        )

    # Send detail alert
    SlackAPIPostOperator(
        task_id=f"slack_alert_{dataset}",
        channel=slack_channel,
        text=detail_text + scala_text
    ).execute(context=context)

def send_default_report_slack_message(
    context,
    custom_report_lines: str,
    detail_template_path: Path,
    slack_channel: str,
    dataset: str,
    alert_user: str,
    base_url: str
) -> None:
    """
    Args:
        context: Airflow context containing execution date and task instance.
        custom_report_lines(str): Custom lines for the default report.
        detail_template_path (str): Path to file containing Slack message template for details.
        slack_channel (str): Slack channel to post messages to.
        alert_user (str): User or group to mention in the alert.
        dataset (str): DBC name of the data.
        base_url (str): User or group to mention in the alert.
    """
    execution_date, airflow_url = get_execution_date_and_airflow_url_from_context(context, base_url)
    # Load and format detail and scala message template
    with open(detail_template_path, 'r') as f:
        detail_text = f.read().format(
            task_id=context['task_instance'].task_id,
            dag_id=context['task_instance'].dag_id,
            airflow_log_url=airflow_url,
            alert_user=alert_user,
            execution_date=execution_date,
            custom_report_lines=custom_report_lines
        )
    # Send detail alert
    SlackAPIPostOperator(
        task_id=f"slack_alert_{dataset}",
        channel=slack_channel,
        text=detail_text
    ).execute(context=context)

def get_execution_date_and_airflow_url_from_context(context, base_url):
    execution_date = context['execution_date']
    params = {
        "dag_id": context['ti'].dag_id,
        "execution_date": execution_date,
    }
    safe_query = urlencode(params)
    airflow_url = f"{base_url}/tree?{safe_query}"
    return execution_date, airflow_url

def create_success_slack_notification(title, slack_channel, base_url, alert_user):
    """
    Returns a function that can be used in PythonOperators to send a notification
    to Slack indicating a task/pipeline was successfully completed.
    """
    def slack_alert(**kwargs):
        if not slack_channel:
            logger.warning(f'slack_channel ({slack_channel}) not set')
            return

        try:
            SlackHook.get_connection('slack_api_default')
        except AirflowNotFoundException as anfe:
            logger.warning(f'Missing slack connection, is SLACK_TOKEN on the environment? Check startup logs.', anfe)
            return

        return fire_success_slack_notification(
            title,
            slack_channel,
            base_url,
            kwargs,  # contains Airflow context
            alert_user
        )

    return slack_alert


def create_task_slack_alert(slack_channel, base_url, alert_user):
    """
    Sends message to a slack channel.
    If you want to send it to a "user" -> use "@user",
        if "public channel" -> use "#channel",
        if "private channel" -> use "channel"
    """

    def task_slack_alert(context, params=None):
        if not slack_channel:
            logger.warning(f'slack_channel ({slack_channel}) not set')
            return

        if 'stopgap' in context.get('task_instance').task_id:
            return fire_task_stopgap_slack_alert(slack_channel, base_url, context, alert_user)
        else:
            return fire_task_fail_slack_alert(slack_channel, base_url, context, alert_user)

    return task_slack_alert


def post_slack(slack_channel, airflow_base_url, tittle, message, circle='green_circle'):
    if not slack_channel:
        logger.warning(f'slack_channel ({slack_channel}) not set')
        return

    try:
        SlackHook.get_connection(SlackHook.default_conn_name) # 'slack_api_default'
    except AirflowNotFoundException as anfe:
        logger.warning(msg=f'Missing slack connection, is SLACK_TOKEN on the environment? Check startup logs.',
                       exc_info=anfe)
        return

    SlackAPIPostOperator(
        task_id=tittle,
        channel=slack_channel,
        text=f"""
                :{circle}: *{tittle}*.
                {message}
                *Airflow Url*: {airflow_base_url}
                """
    ).execute(context=None)
