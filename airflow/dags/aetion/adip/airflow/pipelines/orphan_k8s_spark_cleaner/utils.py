from __future__ import annotations

from functools import cached_property
import logging
from dataclasses import dataclass, asdict
from datetime import datetime
from typing import ClassVar

from airflow.models.taskinstance import TaskInstance
from airflow.providers.cncf.kubernetes.hooks.kubernetes import KubernetesHook
from airflow.utils.session import NEW_SESSION, provide_session
from kubernetes.client import V1Pod
from kubernetes.client.exceptions import ApiException
from sqlalchemy.orm import Session
from enum import Enum


class CleanupStatus(str, Enum):
    SKIPPED = "skipped"
    CLEANED = "cleaned"
    ERROR = "error"


@dataclass
class PodSummary:
    pod_name: str
    pod_namespace: str
    pod_status: str
    adip_instance_id: str
    dag_id: str
    task_id: str
    run_id: str
    try_number: int
    spark_app_name: str
    pod_age_seconds: int


@dataclass
class CleanupSummary:
    __version__: ClassVar[int] = 1
    pod: PodSummary
    status: CleanupStatus
    exception: Exception | None = None

    def serialize(self) -> dict:
        """
        Convert to JSON-safe dict for Airflow.
        """
        return {
            "pod": asdict(self.pod),
            "status": self.status.value,
            "exception": str(self.exception) if self.exception else None,
        }

    @staticmethod
    def deserialize(data: dict, version: int) -> "CleanupSummary":
        """
        Restore from dict to a CleanupSummary.
        """
        return CleanupSummary(
            pod=PodSummary(**data["pod"]),
            status=CleanupStatus(data["status"]),
            exception=Exception(data["exception"]) if data["exception"] else None,
        )

@dataclass
class EnrichedPod:
    base: V1Pod

    @cached_property
    def dag_id(self) -> str | None:
        return self.annotations.get("aetion.com/airflow-dag-id", None)

    @cached_property
    def task_id(self) -> str | None:
        return self.annotations.get("aetion.com/airflow-task-id", None)

    @cached_property
    def run_id(self) -> str | None:
        return self.annotations.get("aetion.com/airflow-run-id", None)

    @cached_property
    def adip_instance_id(self) -> str | None:
        return self.annotations.get("aetion.com/adip-instance-id", None)

    @cached_property
    def try_number(self) -> int | None:
        try:
            return int(self.annotations.get("aetion.com/airflow-try-number", -1))
        except ValueError:
            logging.warning(
                f"Invalid try_number annotation value: {self.annotations.get('aetion.com/airflow-try-number', 'None')}"
            )
            return None

    @cached_property
    def spark_app_name(self) -> str | None:
        return self.labels.get("sparkoperator.k8s.io/app-name", None)

    @cached_property
    def pod_name(self) -> str | None:
        return self.base.metadata.name if self.base.metadata else None

    @cached_property
    def pod_namespace(self) -> str | None:
        return self.base.metadata.namespace if self.base.metadata else None

    @cached_property
    def creation_timestamp(self) -> datetime | None:
        return self.base.metadata.creation_timestamp if self.base.metadata else None

    @property
    def pod_age_seconds(self) -> int:
        if not self.creation_timestamp:
            return 0
        current_time = datetime.now(self.creation_timestamp.tzinfo)
        return int((current_time - self.creation_timestamp).total_seconds())

    @cached_property
    def pod_status(self) -> str | None:
        return self.base.status.phase if self.base.status else None

    @property
    def annotations(self) -> dict[str, str]:
        return self.base.metadata.annotations or {} if self.base.metadata else {}

    @property
    def labels(self) -> dict[str, str]:
        return self.base.metadata.labels or {} if self.base.metadata else {}

    def summary(self) -> PodSummary:
        return PodSummary(
            pod_name=self.pod_name or "",
            pod_namespace=self.pod_namespace or "",
            pod_status=self.pod_status or "",
            adip_instance_id=self.adip_instance_id or "",
            dag_id=self.dag_id or "",
            task_id=self.task_id or "",
            run_id=self.run_id or "",
            try_number=self.try_number or 0,
            spark_app_name=self.spark_app_name or "",
            pod_age_seconds=self.pod_age_seconds or 0,
        )


logger = logging.getLogger(__name__)


def cleanup_orphaned_spark_jobs(
    adip_instance_id: str,
    k8s_hook: KubernetesHook,
    min_pod_age_seconds: int = 900,
    dry_run: bool = True,
):
    """
    Main cleanup function for orphaned Spark jobs.

    This function implements the core logic for identifying and cleaning up
    orphaned Spark jobs by examining Spark driver pods and their associated
    Airflow task states.

    Args:
        adip_instance_id: Current ADIP instance ID.
        k8s_hook: Kubernetes hook for API operations.
        min_pod_age_seconds: Minimum age in seconds before a pod can be cleaned up.
        dry_run: If True, log actions without performing actual cleanup.

    Returns:
        List[CleanupSummary]: A list of cleanup results for each processed pod.

    Raises:
        Exception: When Kubernetes operations fail or database queries encounter errors.
    """

    logger.info(
        f"Starting Spark cleanup watchdog with min_pod_age_seconds={min_pod_age_seconds}, dry_run={dry_run}"
    )
    logger.info(f"ADIP Instance ID: {adip_instance_id}")

    try:
        # List Spark driver pods with label selector
        label_selector = "spark-role=driver"
        logger.info(f"Listing Spark driver pods with label selector: {label_selector}")

        # Get the core API client and list pods
        core_api = k8s_hook.core_v1_client
        pod_list = core_api.list_namespaced_pod(
            namespace="spark-operator", label_selector=label_selector
        )

        pods = (
            EnrichedPod(base=pod)
            for pod in pod_list.items
            if pod.metadata and pod.metadata.name and pod.metadata.namespace
        )

        # Filter pods by current ADIP instance ID
        pods = (pod for pod in pods if pod.adip_instance_id == adip_instance_id)

        # spark_app_names is defined
        pods = (pod for pod in pods if pod.spark_app_name)

        # airflow annotations are defined
        pods = (
            pod
            for pod in pods
            if pod.dag_id and pod.task_id and pod.run_id and pod.try_number
        )

        # filter out pods that are too young
        pods = (pod for pod in pods if pod.pod_age_seconds >= min_pod_age_seconds)

        # include only pods associated to failed task instances
        pods = (
            pod
            for pod in pods
            if _get_task_instance_state(
                pod.dag_id,  # type: ignore
                pod.task_id,  # type: ignore
                pod.run_id,  # type: ignore
                pod.try_number,  # type: ignore
            )
            == "failed"
        )

        final_pods = list(pods)
        logger.info(f"Found {len(final_pods)} Spark driver pods matching criteria")
        logger.info(
            f"Pods: {[pod.pod_name for pod in final_pods]} in namespace {final_pods[0].pod_namespace if final_pods else 'N/A'}"
        )
        if dry_run:
            return [
                CleanupSummary(pod=pod.summary(), status=CleanupStatus.CLEANED)
                for pod in final_pods
            ]
        else:
            return [
                CleanupSummary(pod=pod.summary(), status=status, exception=exception)
                for pod, (status, exception) in (
                    (pod, _delete_spark_application(pod, k8s_hook))
                    for pod in final_pods
                )
            ]

    except ApiException as e:
        logger.error(f"Kubernetes API error: {e}")
        raise
    except Exception as e:
        logger.error(f"Unexpected error during cleanup: {e}", exc_info=True)
        raise


@provide_session
def _get_task_instance_state(
    dag_id: str,
    task_id: str,
    run_id: str,
    try_number: int,
    *,
    session: Session = NEW_SESSION,
) -> str | None:
    """
    Query Airflow database for task instance state.

    Args:
        dag_id: DAG identifier.
        task_id: Task identifier.
        run_id: DAG run identifier.
        try_number: Task try number.
        session: SQLAlchemy session for database access.

    Returns:
        Task state string or None if not found.
    """
    try:
        task_instance = (
            session.query(TaskInstance)
            .filter_by(
                dag_id=dag_id,
                task_id=task_id,
                run_id=run_id,
                try_number=try_number,
            )
            .first()
        )

        if task_instance:
            logger.debug(
                f"Found task instance: {dag_id}::{task_id} state={task_instance.state}"
            )
            return task_instance.state
        else:
            logger.warning(
                f"Task instance not found: {dag_id}::{task_id} run_id={run_id} try_number={try_number}"
            )
            return None

    except Exception as e:
        logger.error(f"Database query error for task {dag_id}::{task_id}: {e}")
        return None


def _delete_spark_application(
    pod: EnrichedPod, k8s_hook: KubernetesHook
) -> tuple[CleanupStatus, Exception | None]:
    """
    Delete a SparkApplication custom resource.

    Args:
        pod: EnrichedPod object containing pod metadata.
        k8s_hook: Kubernetes hook for API operations.

    Returns:
        Tuple[CleanupStatus, Exception | None]: Status of the operation and any exception.
    """
    try:
        assert pod.spark_app_name, "SparkApplication name must be set"
        assert pod.pod_namespace, "Pod namespace must be set"
        # Use the KubernetesHook's delete_custom_object method
        logger.warning(
            f"Deleting SparkApplication {pod.spark_app_name} in namespace {pod.pod_namespace}"
        )

        k8s_hook.delete_custom_object(
            group="sparkoperator.k8s.io",
            version="v1beta2",
            namespace=pod.pod_namespace,
            plural="sparkapplications",
            name=pod.spark_app_name,
        )

        logger.info(f"Successfully deleted SparkApplication {pod.spark_app_name}")
        return CleanupStatus.CLEANED, None

    except ApiException as e:
        if e.status == 404:
            logger.warning(
                f"SparkApplication {pod.spark_app_name} not found (already deleted?)"
            )
            return CleanupStatus.CLEANED, None
        else:
            logger.error(f"Failed to delete SparkApplication {pod.spark_app_name}: {e}")
            return CleanupStatus.ERROR, e
    except Exception as e:
        logger.error(
            f"Unexpected error deleting SparkApplication {pod.spark_app_name}: {e}"
        )
        return CleanupStatus.ERROR, e


def _generate_report(
    pod_summaries: list[PodSummary], airflow_base_url: str
) -> str | None:
    if not pod_summaries:
        return None

    adip_instance_id = pod_summaries[0].adip_instance_id
    report = f"Cleaned {len(pod_summaries)} orphaned Spark jobs in ADIP instance {adip_instance_id}:\n\n"
    for summary in pod_summaries:
        url = _build_task_url(
            airflow_base_url,
            summary.dag_id,
            summary.task_id,
            summary.run_id,
        )
        report += f"- {summary.pod_name}\n"
        report += f"  - DAG: `{summary.dag_id}`\n"
        report += f"  - Task: `{summary.task_id}`\n"
        report += f"  - Run: `{summary.run_id}`\n"
        report += f"  - Try: `{summary.try_number}`\n"
        report += f"  - URL: <{url}| View in Airflow>\n"
        report += "\n"
    return report


def _build_task_url(base_url: str, dag_id: str, task_id: str, run_id: str) -> str:
    """
    Build a URL to the Airflow log page for a specific task instance.
    """
    from urllib.parse import urlencode

    base_url = f"{base_url}/dags/{dag_id}/grid"
    params = {
        "dag_id": dag_id,
        "task_id": task_id,
        "dag_run_id": run_id,
        "tab": "logs",
    }

    url = f"{base_url}?{urlencode(params)}"

    return url
