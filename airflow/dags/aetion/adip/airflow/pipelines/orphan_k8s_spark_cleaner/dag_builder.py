from __future__ import annotations

from datetime import datetime, timedelta
import logging
from airflow.decorators import dag, task
from airflow.providers.cncf.kubernetes.hooks.kubernetes import KubernetesHook
from airflow.models import Variable
from .utils import (
    CleanupStatus,
    SparkDeletionResult,
    _generate_report,
    cleanup_orphaned_spark_jobs,
)
from airflow.operators.python import get_current_context

from operators.slack import post_slack


logger = logging.getLogger(__name__)


@dag(
    schedule_interval=None,
    catchup=False,
    tags=["monitoring", "k8s", "spark"],
    default_args={
        "owner": "airflow",
        "retries": 0,
        "retry_delay": timedelta(minutes=5),
        "on_failure_callback": lambda context: logger.error(
            f"Task failed: {context['task_instance'].task_id} in DAG: {context['task_instance'].dag_id}"
        ),
        "depends_on_past": False,
        "catchup_by_default": False,
        "start_date": datetime(2015, 6, 1),
        "execution_timeout": timedelta(hours=1),
        "max_active_runs": 1,  # Prevent overlapping runs
        "max_active_tasks": 1,  # Limit concurrent tasks
    },
)
def clean_orphan_k8s_spark_jobs():
    @task(
        provide_context=True,
        task_id="clean_orphan_k8s_spark_jobs",
        doc_md="""
        ## Orphan K8S Spark Cleaner

        This task monitors and cleans up orphaned Spark jobs in the Kubernetes cluster.

        **Safety Measures:**
        - Only processes pods from the current ADIP instance
        - Requires both failed task state AND minimum pod age
        - Comprehensive logging of all actions
        - Dry run mode available for testing

        **Parameters:**
        - `min_pod_age_seconds`: Minimum age before cleanup (default: 900 seconds)
        - `dry_run`: Test mode without actual deletions (default: True)
        """,
    )
    def clean():
        ctx = get_current_context()
        dag_run = ctx.get("dag_run")
        dag_run_conf = dag_run.conf if dag_run and hasattr(dag_run, "conf") else {}
        min_pod_age_seconds = dag_run_conf.get("min_pod_age_seconds", 900)
        dry_run = dag_run_conf.get("dry_run", True)

        adip_instance_id = Variable.get("adip_instance_id", "no-adip-id-likely-to-fail")

        k8s_hook = KubernetesHook(conn_id="kubernetes_default", in_cluster=True)

        deletion_results = cleanup_orphaned_spark_jobs(
            adip_instance_id=adip_instance_id,
            k8s_hook=k8s_hook,
            min_pod_age_seconds=min_pod_age_seconds,
            dry_run=dry_run,
        )

        return deletion_results

    @task(
        task_id="notify_to_slack",
        doc_md="""
        ## Notify to Slack

        This task sends a notification to Slack with the results of the orphan K8S Spark job cleanup.
        It receives a list of `SparkDeletionResult` objects and posts a summary report.
        """,
    )
    def notify_to_slack(cleanup_results: list[SparkDeletionResult]):
        ADIP_INSTANCE_ID = Variable.get("adip_instance_id", "no-adip-id-likely-to-fail")
        SLACK_CHANNEL = Variable.get("slack_channel", "#adip-notifications")
        AIRFLOW_BASE_URL = Variable.get("airflow_base_url", "http://localhost:8080")

        reportable_results = [
            r for r in cleanup_results if r.status == CleanupStatus.CLEANED
        ]

        if not reportable_results:
            logger.info("No orphaned Spark jobs have been cleaned up.")
            return

        pods = [r.pod for r in reportable_results]
        report = _generate_report(pods, AIRFLOW_BASE_URL)
        post_slack(
            SLACK_CHANNEL,
            AIRFLOW_BASE_URL,
            tittle=f"Cleanup Kubernetes Orphan Spark Jobs in ADIP ({ADIP_INSTANCE_ID})",
            message=report,
        )

    raw_report = clean()
    notify_to_slack(raw_report)  # pyright: ignore[reportArgumentType]
