"""
Spark Job Cleanup Watchdog DAG.

This module implements an automated cleanup system for orphaned Spark jobs running
in Kubernetes. It monitors Spark driver pods and removes associated SparkApplication
resources when the corresponding Airflow tasks have failed and the pods exceed
the configured age threshold.

The watchdog operates by:
1. Listing all Spark driver pods in the Kubernetes cluster
2. Extracting Airflow metadata from pod annotations
3. Querying the Airflow database for task instance states
4. Cleaning up SparkApplication resources for failed tasks that exceed age limits

This ensures that failed Spark jobs don't consume cluster resources indefinitely
while maintaining safety through multiple validation checks.
"""

from __future__ import annotations

from functools import lru_cache
import logging
from datetime import datetime, timedelta, timezone
from typing import Any, Optional

import pendulum
from airflow import DAG
from airflow.models import Variable
from airflow.models.taskinstance import TaskInstance
from airflow.operators.python import PythonOperator
from airflow.providers.cncf.kubernetes.hooks.kubernetes import KubernetesHook
from airflow.utils.session import create_session
from kubernetes.client import V1Pod
from kubernetes.client.exceptions import ApiException
from pipelines.helpers import get_dag_default_args


@lru_cache(maxsize=1)
def get_adip_instance_id() -> str:
    """
    Retrieve the ADIP instance ID from Airflow Variables.

    Returns:
        str: The ADIP instance ID or a default value if not set.
    """
    return Variable.get("adip_instance_id", "no-adip-id-likely-to-fail")


logger = logging.getLogger(__name__)


def cleanup_orphaned_spark_jobs(**context: Any) -> None:
    """
    Main cleanup function for orphaned Spark jobs.

    This function implements the core logic for identifying and cleaning up
    orphaned Spark jobs by examining Spark driver pods and their associated
    Airflow task states.

    Args:
        **context: Airflow task context containing DAG run parameters and metadata.

    Raises:
        Exception: When Kubernetes operations fail or database queries encounter errors.
    """
    # Get DAG parameters with defaults
    adip_instance_id = get_adip_instance_id()
    dag_run = context["dag_run"]
    min_pod_age_seconds = dag_run.conf.get(
        "min_pod_age_seconds", 900
    )  # 15 minutes default
    dry_run = dag_run.conf.get("dry_run", True)  # Default to dry run

    logger.info(
        f"Starting Spark cleanup watchdog with min_pod_age_seconds={min_pod_age_seconds}, dry_run={dry_run}"
    )
    logger.info(f"ADIP Instance ID: {adip_instance_id}")

    # Initialize Kubernetes hook
    k8s_hook = KubernetesHook(conn_id="kubernetes_default", in_cluster=True)

    try:
        # List Spark driver pods with label selector
        label_selector = "spark-role=driver"
        logger.info(f"Listing Spark driver pods with label selector: {label_selector}")

        # Get the core API client and list pods
        core_api = k8s_hook.core_v1_client
        pod_list = core_api.list_namespaced_pod(
            namespace="spark-operator", label_selector=label_selector
        )

        logger.info(f"Found {len(pod_list.items)} Spark driver pods")

        # Process each pod
        cleanup_count = 0
        skip_count = 0

        for pod in pod_list.items:
            try:
                result = _process_spark_pod(pod, min_pod_age_seconds, dry_run, k8s_hook, adip_instance_id)
                if result == "cleaned":
                    cleanup_count += 1
                elif result == "skipped":
                    skip_count += 1
            except Exception as e:
                logger.error(
                    f"Error processing pod {pod.metadata.name}: {e}", exc_info=True
                )
                continue

        logger.info(
            f"Cleanup completed: {cleanup_count} jobs cleaned, {skip_count} jobs skipped"
        )

    except ApiException as e:
        logger.error(f"Kubernetes API error: {e}")
        raise
    except Exception as e:
        logger.error(f"Unexpected error during cleanup: {e}", exc_info=True)
        raise


def _process_spark_pod(
    pod: V1Pod, min_pod_age_seconds: int, dry_run: bool, k8s_hook: KubernetesHook,
    adip_instance_id: str,
) -> str:
    """
    Process a single Spark driver pod for potential cleanup.

    Args:
        pod: Kubernetes pod object to process.
        min_pod_age_seconds: Minimum age in seconds before a pod can be cleaned up.
        dry_run: If True, log actions without performing actual cleanup.
        k8s_hook: Kubernetes hook for API operations.
        adip_instance_id: Current ADIP instance ID.

    Returns:
        str: Result of processing - "cleaned", "skipped", or "error".
    """
    pod_name = pod.metadata.name if pod.metadata else None
    pod_namespace = pod.metadata.namespace if pod.metadata else None

    if not pod_name or not pod_namespace:
        logger.error("Pod metadata is missing name or namespace, skipping")
        return "skipped"

    logger.info(f"Processing pod: {pod_name} in namespace: {pod_namespace}")

    # Extract required annotations
    annotations = (pod.metadata.annotations or {}) if pod.metadata else {}
    required_annotations = [
        "aetion.com/airflow-dag-id",
        "aetion.com/airflow-task-id",
        "aetion.com/airflow-run-id",
        "aetion.com/adip-instance-id",
        "aetion.com/airflow-try-number",
    ]

    # Check for missing annotations
    missing_annotations = [
        ann for ann in required_annotations if ann not in annotations
    ]
    if missing_annotations:
        logger.warning(
            f"Pod {pod_name} missing required annotations: {missing_annotations}"
        )
        return "skipped"

    # Extract annotation values
    dag_id = annotations["aetion.com/airflow-dag-id"]
    task_id = annotations["aetion.com/airflow-task-id"]
    run_id = annotations["aetion.com/airflow-run-id"]
    pod_adip_instance_id = annotations["aetion.com/adip-instance-id"]
    try_number = int(annotations["aetion.com/airflow-try-number"])

    # Safety check: only process pods from current ADIP instance
    if pod_adip_instance_id != adip_instance_id:
        logger.info(
            f"Skipping pod {pod_name}: adip-instance-id mismatch ({pod_adip_instance_id} != {adip_instance_id})"
        )
        return "skipped"

    # Check pod age
    creation_time = pod.metadata.creation_timestamp if pod.metadata else None
    creation_time = creation_time or datetime.now(timezone.utc)
    current_time = datetime.now(creation_time.tzinfo)
    pod_age_seconds = (current_time - creation_time).total_seconds()

    if pod_age_seconds < min_pod_age_seconds:
        logger.info(
            f"Pod {pod_name} too young: {pod_age_seconds}s < {min_pod_age_seconds}s"
        )
        return "skipped"

    logger.info(f"Pod {pod_name} age: {pod_age_seconds}s, checking task state...")

    # Query Airflow database for task instance state
    task_state = _get_task_instance_state(dag_id, task_id, run_id, try_number)

    if task_state != "failed":
        logger.info(
            f"Pod {pod_name} task state is '{task_state}', not 'failed' - skipping"
        )
        return "skipped"

    logger.warning(
        f"Pod {pod_name} meets cleanup criteria: failed task, age {pod_age_seconds}s"
    )

    # Extract SparkApplication name from labels
    spark_app_name = _extract_spark_application_name(pod)
    if not spark_app_name:
        logger.error(f"Could not extract SparkApplication name for pod {pod_name}")
        return "error"

    # Perform cleanup
    if dry_run:
        logger.info(
            f"DRY RUN: Would delete SparkApplication {spark_app_name} in namespace {pod_namespace}"
        )
        return "cleaned"
    else:
        return _delete_spark_application(spark_app_name, pod_namespace, k8s_hook)


def _get_task_instance_state(
    dag_id: str, task_id: str, run_id: str, try_number: int
) -> Optional[str]:
    """
    Query Airflow database for task instance state.

    Args:
        dag_id: DAG identifier.
        task_id: Task identifier.
        run_id: DAG run identifier.
        try_number: Task try number.

    Returns:
        Task state string or None if not found.
    """
    try:
        with create_session() as session:
            task_instance = (
                session.query(TaskInstance)
                .filter(
                    TaskInstance.dag_id == dag_id,
                    TaskInstance.task_id == task_id,
                    TaskInstance.run_id == run_id,
                    TaskInstance.try_number == try_number,
                )
                .first()
            )

            if task_instance:
                logger.debug(
                    f"Found task instance: {dag_id}.{task_id} state={task_instance.state}"
                )
                return task_instance.state
            else:
                logger.warning(
                    f"Task instance not found: {dag_id}.{task_id}.{run_id}.{try_number}"
                )
                return None

    except Exception as e:
        logger.error(f"Database query error for task {dag_id}.{task_id}: {e}")
        return None


def _extract_spark_application_name(pod: V1Pod) -> Optional[str]:
    """
    Extract SparkApplication name from pod labels.

    Args:
        pod: Kubernetes pod object.

    Returns:
        SparkApplication name or None if not found.
    """
    labels = pod.metadata.labels or {} if pod.metadata else {}
    pod_name = pod.metadata.name if pod.metadata else None
    spark_app_name = labels.get("sparkoperator.k8s.io/app-name")

    if not spark_app_name:
        logger.error(
            f"Pod {pod_name} missing 'sparkoperator.k8s.io/app-name' label"
        )
        return None

    return spark_app_name


def _delete_spark_application(
    app_name: str, namespace: str, k8s_hook: KubernetesHook
) -> str:
    """
    Delete a SparkApplication custom resource.

    Args:
        app_name: Name of the SparkApplication to delete.
        namespace: Kubernetes namespace.
        k8s_hook: Kubernetes hook for API operations.

    Returns:
        Result string indicating success or failure.
    """
    try:
        # Use the KubernetesHook's delete_custom_object method
        logger.warning(f"Deleting SparkApplication {app_name} in namespace {namespace}")

        k8s_hook.delete_custom_object(
            group="sparkoperator.k8s.io",
            version="v1beta2",
            namespace=namespace,
            plural="sparkapplications",
            name=app_name,
        )

        logger.info(f"Successfully deleted SparkApplication {app_name}")
        return "cleaned"

    except ApiException as e:
        if e.status == 404:
            logger.warning(f"SparkApplication {app_name} not found (already deleted?)")
            return "cleaned"
        else:
            logger.error(f"Failed to delete SparkApplication {app_name}: {e}")
            return "error"
    except Exception as e:
        logger.error(f"Unexpected error deleting SparkApplication {app_name}: {e}")
        return "error"


# DAG definition
def failure_callback(context):
    """Simple failure callback for the watchdog DAG."""
    logger.error(f"Spark cleanup watchdog failed: {context}")


dag_default_args = get_dag_default_args(failure_callback)

dag = DAG(
    dag_id="spark_job_cleanup_watchdog",
    default_args=dag_default_args,
    description="Automated cleanup of orphaned Spark jobs in Kubernetes",
    schedule_interval=timedelta(minutes=15),  # Run every 15 minutes
    start_date=pendulum.datetime(2024, 1, 1, tz="UTC"),
    catchup=False,
    max_active_runs=1,  # Prevent overlapping runs
    params={
        "min_pod_age_seconds": 900,  # 15 minutes default
        "dry_run": False,
    },
    tags=["spark", "cleanup", "kubernetes", "watchdog"],
)

cleanup_task = PythonOperator(
    task_id="cleanup_orphaned_spark_jobs",
    python_callable=cleanup_orphaned_spark_jobs,
    dag=dag,
    doc_md="""
    ## Spark Job Cleanup Watchdog

    This task monitors and cleans up orphaned Spark jobs in the Kubernetes cluster.

    **Safety Measures:**
    - Only processes pods from the current ADIP instance
    - Requires both failed task state AND minimum pod age
    - Comprehensive logging of all actions
    - Dry run mode available for testing

    **Parameters:**
    - `min_pod_age_seconds`: Minimum age before cleanup (default: 900 seconds)
    - `dry_run`: Test mode without actual deletions (default: False)
    """,
)
